ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES
CLANG_CXX_LANGUAGE_STANDARD = c++20
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/hermes-engine/destroot/Library/Frameworks/universal" "${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 $(inherited) SD_WEBP=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/DoubleConversion" "${PODS_ROOT}/Headers/Public/EXConstants" "${PODS_ROOT}/Headers/Public/Expo" "${PODS_ROOT}/Headers/Public/ExpoFileSystem" "${PODS_ROOT}/Headers/Public/ExpoModulesCore" "${PODS_ROOT}/Headers/Public/FBLazyVector" "${PODS_ROOT}/Headers/Public/RCT-Folly" "${PODS_ROOT}/Headers/Public/RCTDeprecation" "${PODS_ROOT}/Headers/Public/RCTRequired" "${PODS_ROOT}/Headers/Public/RCTTypeSafety" "${PODS_ROOT}/Headers/Public/RNCAsyncStorage" "${PODS_ROOT}/Headers/Public/RNGestureHandler" "${PODS_ROOT}/Headers/Public/RNReanimated" "${PODS_ROOT}/Headers/Public/RNScreens" "${PODS_ROOT}/Headers/Public/React-Core" "${PODS_ROOT}/Headers/Public/React-Fabric" "${PODS_ROOT}/Headers/Public/React-FabricComponents" "${PODS_ROOT}/Headers/Public/React-FabricImage" "${PODS_ROOT}/Headers/Public/React-ImageManager" "${PODS_ROOT}/Headers/Public/React-Mapbuffer" "${PODS_ROOT}/Headers/Public/React-NativeModulesApple" "${PODS_ROOT}/Headers/Public/React-RCTAnimation" "${PODS_ROOT}/Headers/Public/React-RCTAppDelegate" "${PODS_ROOT}/Headers/Public/React-RCTBlob" "${PODS_ROOT}/Headers/Public/React-RCTFBReactNativeSpec" "${PODS_ROOT}/Headers/Public/React-RCTFabric" "${PODS_ROOT}/Headers/Public/React-RCTRuntime" "${PODS_ROOT}/Headers/Public/React-RCTText" "${PODS_ROOT}/Headers/Public/React-RuntimeApple" "${PODS_ROOT}/Headers/Public/React-RuntimeCore" "${PODS_ROOT}/Headers/Public/React-RuntimeHermes" "${PODS_ROOT}/Headers/Public/React-callinvoker" "${PODS_ROOT}/Headers/Public/React-cxxreact" "${PODS_ROOT}/Headers/Public/React-debug" "${PODS_ROOT}/Headers/Public/React-defaultsnativemodule" "${PODS_ROOT}/Headers/Public/React-domnativemodule" "${PODS_ROOT}/Headers/Public/React-featureflags" "${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule" "${PODS_ROOT}/Headers/Public/React-graphics" "${PODS_ROOT}/Headers/Public/React-hermes" "${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule" "${PODS_ROOT}/Headers/Public/React-jserrorhandler" "${PODS_ROOT}/Headers/Public/React-jsi" "${PODS_ROOT}/Headers/Public/React-jsiexecutor" "${PODS_ROOT}/Headers/Public/React-jsinspector" "${PODS_ROOT}/Headers/Public/React-jsinspectortracing" "${PODS_ROOT}/Headers/Public/React-jsitooling" "${PODS_ROOT}/Headers/Public/React-logger" "${PODS_ROOT}/Headers/Public/React-microtasksnativemodule" "${PODS_ROOT}/Headers/Public/React-oscompat" "${PODS_ROOT}/Headers/Public/React-perflogger" "${PODS_ROOT}/Headers/Public/React-performancetimeline" "${PODS_ROOT}/Headers/Public/React-rendererconsistency" "${PODS_ROOT}/Headers/Public/React-renderercss" "${PODS_ROOT}/Headers/Public/React-rendererdebug" "${PODS_ROOT}/Headers/Public/React-runtimeexecutor" "${PODS_ROOT}/Headers/Public/React-runtimescheduler" "${PODS_ROOT}/Headers/Public/React-timing" "${PODS_ROOT}/Headers/Public/React-utils" "${PODS_ROOT}/Headers/Public/ReactAppDependencyProvider" "${PODS_ROOT}/Headers/Public/ReactCodegen" "${PODS_ROOT}/Headers/Public/ReactCommon" "${PODS_ROOT}/Headers/Public/SDWebImage" "${PODS_ROOT}/Headers/Public/SDWebImageAVIFCoder" "${PODS_ROOT}/Headers/Public/SDWebImageSVGCoder" "${PODS_ROOT}/Headers/Public/SDWebImageWebPCoder" "${PODS_ROOT}/Headers/Public/SocketRocket" "${PODS_ROOT}/Headers/Public/Yoga" "${PODS_ROOT}/Headers/Public/boost" "${PODS_ROOT}/Headers/Public/fast_float" "${PODS_ROOT}/Headers/Public/fmt" "${PODS_ROOT}/Headers/Public/glog" "${PODS_ROOT}/Headers/Public/hermes-engine" "${PODS_ROOT}/Headers/Public/libavif" "${PODS_ROOT}/Headers/Public/libdav1d" "${PODS_ROOT}/Headers/Public/libwebp" "${PODS_ROOT}/Headers/Public/react-native-netinfo" "${PODS_ROOT}/Headers/Public/react-native-safe-area-context" "${PODS_ROOT}/Headers/Public/react-native-webview" "$(PODS_ROOT)/DoubleConversion" "${PODS_CONFIGURATION_BUILD_DIR}/Expo/Swift Compatibility Header" "$(PODS_ROOT)/Headers/Private/Yoga" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore/Swift Compatibility Header" "$(PODS_ROOT)/Headers/Private/Yoga" "$(PODS_ROOT)/boost" "$(PODS_ROOT)/boost" "$(PODS_ROOT)/boost-for-react-native" "$(PODS_ROOT)/glog" "$(PODS_ROOT)/RCT-Folly" "$(PODS_ROOT)/Headers/Public/React-hermes" "$(PODS_ROOT)/Headers/Public/hermes-engine" "$(PODS_ROOT)/../../node_modules/react-native/ReactCommon" "$(PODS_ROOT)/../../node_modules/react-native-reanimated/apple" "$(PODS_ROOT)/../../node_modules/react-native-reanimated/Common/cpp" "$(PODS_ROOT)/Headers/Private/React-Core" "$(PODS_ROOT)/Headers/Private/React-Core" "$(PODS_ROOT)/Headers/Private/Yoga"
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift '@executable_path/Frameworks' '@loader_path/Frameworks'
LIBRARY_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion" "${PODS_CONFIGURATION_BUILD_DIR}/EXConstants" "${PODS_CONFIGURATION_BUILD_DIR}/Expo" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoBlur" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoCrypto" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoHaptics" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoHead" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoImage" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinking" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSecureStore" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSplashScreen" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSymbols" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSystemUI" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoVideo" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoWebBrowser" "${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly" "${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation" "${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety" "${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage" "${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler" "${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated" "${PODS_CONFIGURATION_BUILD_DIR}/RNScreens" "${PODS_CONFIGURATION_BUILD_DIR}/React-Core" "${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager" "${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer" "${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTRuntime" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes" "${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact" "${PODS_CONFIGURATION_BUILD_DIR}/React-debug" "${PODS_CONFIGURATION_BUILD_DIR}/React-defaultsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-domnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflagsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics" "${PODS_CONFIGURATION_BUILD_DIR}/React-hermes" "${PODS_CONFIGURATION_BUILD_DIR}/React-idlecallbacksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsi" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling" "${PODS_CONFIGURATION_BUILD_DIR}/React-logger" "${PODS_CONFIGURATION_BUILD_DIR}/React-microtasksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-oscompat" "${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger" "${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency" "${PODS_CONFIGURATION_BUILD_DIR}/React-renderercss" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug" "${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler" "${PODS_CONFIGURATION_BUILD_DIR}/React-utils" "${PODS_CONFIGURATION_BUILD_DIR}/ReactAppDependencyProvider" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCodegen" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageAVIFCoder" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageSVGCoder" "${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder" "${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket" "${PODS_CONFIGURATION_BUILD_DIR}/Yoga" "${PODS_CONFIGURATION_BUILD_DIR}/fmt" "${PODS_CONFIGURATION_BUILD_DIR}/glog" "${PODS_CONFIGURATION_BUILD_DIR}/libavif" "${PODS_CONFIGURATION_BUILD_DIR}/libdav1d" "${PODS_CONFIGURATION_BUILD_DIR}/libwebp" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context" "${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview" "${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}" /usr/lib/swift
OTHER_CFLAGS = $(inherited) -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/Expo/Expo.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset/ExpoAsset.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoBlur/ExpoBlur.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoCrypto/ExpoCrypto.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem/ExpoFileSystem.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont/ExpoFont.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoHaptics/ExpoHaptics.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoHead/ExpoHead.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoImage/ExpoImage.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake/ExpoKeepAwake.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient/ExpoLinearGradient.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinking/ExpoLinking.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore/ExpoModulesCore.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoSecureStore/ExpoSecureStore.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoSplashScreen/ExpoSplashScreen.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoSymbols/ExpoSymbols.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoSystemUI/ExpoSystemUI.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoVideo/ExpoVideo.modulemap" -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoWebBrowser/ExpoWebBrowser.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Private/SDWebImageSVGCoder/SDWebImageSVGCoder.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Private/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTFabric/React-RCTFabric.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTRuntime/React-RCTRuntime.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React/React-Core.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactAppDependencyProvider/ReactAppDependencyProvider.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCodegen/ReactCodegen.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_FabricComponents/React-FabricComponents.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/SDWebImage/SDWebImage.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/SDWebImageAVIFCoder/SDWebImageAVIFCoder.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/glog/glog.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_tracing/React-jsinspectortracing.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/libavif/libavif.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_defaults/React-defaultsnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_dom/React-domnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_featureflags/React-featureflagsnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_idlecallbacks/React-idlecallbacksnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_microtasks/React-microtasksnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_css/React-renderercss.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_runtime/React-jsitooling.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/reacthermes/React-hermes.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap" $(inherited)  $(inherited) -DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -Wno-comma -Wno-shorten-64-to-32 -DRCT_NEW_ARCH_ENABLED  -DREACT_NATIVE_MINOR_VERSION=79 -DREANIMATED_VERSION=3.17.5 -DNDEBUG
OTHER_CPLUSPLUSFLAGS = $(inherited) -DNDEBUG -DRCT_NEW_ARCH_ENABLED=1 -DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32
OTHER_LDFLAGS = $(inherited) -ObjC -l"DoubleConversion" -l"EXConstants" -l"Expo" -l"ExpoAsset" -l"ExpoBlur" -l"ExpoCrypto" -l"ExpoFileSystem" -l"ExpoFont" -l"ExpoHaptics" -l"ExpoHead" -l"ExpoImage" -l"ExpoKeepAwake" -l"ExpoLinearGradient" -l"ExpoLinking" -l"ExpoModulesCore" -l"ExpoSecureStore" -l"ExpoSplashScreen" -l"ExpoSymbols" -l"ExpoSystemUI" -l"ExpoVideo" -l"ExpoWebBrowser" -l"RCT-Folly" -l"RCTDeprecation" -l"RCTTypeSafety" -l"RNCAsyncStorage" -l"RNGestureHandler" -l"RNReanimated" -l"RNScreens" -l"React-Core" -l"React-CoreModules" -l"React-Fabric" -l"React-FabricComponents" -l"React-FabricImage" -l"React-ImageManager" -l"React-Mapbuffer" -l"React-NativeModulesApple" -l"React-RCTAnimation" -l"React-RCTAppDelegate" -l"React-RCTBlob" -l"React-RCTFBReactNativeSpec" -l"React-RCTFabric" -l"React-RCTImage" -l"React-RCTLinking" -l"React-RCTNetwork" -l"React-RCTRuntime" -l"React-RCTSettings" -l"React-RCTText" -l"React-RCTVibration" -l"React-RuntimeApple" -l"React-RuntimeCore" -l"React-RuntimeHermes" -l"React-cxxreact" -l"React-debug" -l"React-defaultsnativemodule" -l"React-domnativemodule" -l"React-featureflags" -l"React-featureflagsnativemodule" -l"React-graphics" -l"React-hermes" -l"React-idlecallbacksnativemodule" -l"React-jserrorhandler" -l"React-jsi" -l"React-jsiexecutor" -l"React-jsinspector" -l"React-jsinspectortracing" -l"React-jsitooling" -l"React-logger" -l"React-microtasksnativemodule" -l"React-oscompat" -l"React-perflogger" -l"React-performancetimeline" -l"React-rendererconsistency" -l"React-renderercss" -l"React-rendererdebug" -l"React-runtimescheduler" -l"React-utils" -l"ReactAppDependencyProvider" -l"ReactCodegen" -l"ReactCommon" -l"SDWebImage" -l"SDWebImageAVIFCoder" -l"SDWebImageSVGCoder" -l"SDWebImageWebPCoder" -l"SocketRocket" -l"Yoga" -l"c++" -l"c++abi" -l"fmt" -l"glog" -l"icucore" -l"libavif" -l"libdav1d" -l"libwebp" -l"react-native-netinfo" -l"react-native-safe-area-context" -l"react-native-webview" -framework "Accelerate" -framework "AudioToolbox" -framework "CFNetwork" -framework "CoreGraphics" -framework "ImageIO" -framework "MobileCoreServices" -framework "QuartzCore" -framework "Security" -framework "UIKit" -framework "hermes" -weak_framework "JavaScriptCore"
OTHER_MODULE_VERIFIER_FLAGS = $(inherited) "-F${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion" "-F${PODS_CONFIGURATION_BUILD_DIR}/EXConstants" "-F${PODS_CONFIGURATION_BUILD_DIR}/Expo" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoBlur" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoCrypto" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoHaptics" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoHead" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinking" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoSecureStore" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoSplashScreen" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoSymbols" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoSystemUI" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoVideo" "-F${PODS_CONFIGURATION_BUILD_DIR}/ExpoWebBrowser" "-F${PODS_CONFIGURATION_BUILD_DIR}/FBLazyVector" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCTRequired" "-F${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated" "-F${PODS_CONFIGURATION_BUILD_DIR}/RNScreens" "-F${PODS_CONFIGURATION_BUILD_DIR}/React" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-Core" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTActionSheet" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTRuntime" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-callinvoker" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-debug" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-defaultsnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-domnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-featureflagsnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-graphics" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-hermes" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-idlecallbacksnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsi" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-jsitracing" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-logger" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-microtasksnativemodule" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-oscompat" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-renderercss" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-rncore" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-runtimeexecutor" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-timing" "-F${PODS_CONFIGURATION_BUILD_DIR}/React-utils" "-F${PODS_CONFIGURATION_BUILD_DIR}/ReactAppDependencyProvider" "-F${PODS_CONFIGURATION_BUILD_DIR}/ReactCodegen" "-F${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageAVIFCoder" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageSVGCoder" "-F${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder" "-F${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket" "-F${PODS_CONFIGURATION_BUILD_DIR}/Yoga" "-F${PODS_CONFIGURATION_BUILD_DIR}/boost" "-F${PODS_CONFIGURATION_BUILD_DIR}/fast_float" "-F${PODS_CONFIGURATION_BUILD_DIR}/fmt" "-F${PODS_CONFIGURATION_BUILD_DIR}/glog" "-F${PODS_CONFIGURATION_BUILD_DIR}/hermes-engine" "-F${PODS_CONFIGURATION_BUILD_DIR}/libavif" "-F${PODS_CONFIGURATION_BUILD_DIR}/libdav1d" "-F${PODS_CONFIGURATION_BUILD_DIR}/libwebp" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context" "-F${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview"
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/Expo/Expo.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset/ExpoAsset.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoBlur/ExpoBlur.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoCrypto/ExpoCrypto.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem/ExpoFileSystem.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont/ExpoFont.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoHaptics/ExpoHaptics.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoHead/ExpoHead.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoImage/ExpoImage.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake/ExpoKeepAwake.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient/ExpoLinearGradient.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinking/ExpoLinking.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore/ExpoModulesCore.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoSecureStore/ExpoSecureStore.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoSplashScreen/ExpoSplashScreen.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoSymbols/ExpoSymbols.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoSystemUI/ExpoSystemUI.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoVideo/ExpoVideo.modulemap" -Xcc -fmodule-map-file="${PODS_CONFIGURATION_BUILD_DIR}/ExpoWebBrowser/ExpoWebBrowser.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Private/SDWebImageSVGCoder/SDWebImageSVGCoder.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Private/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTFabric/React-RCTFabric.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTRuntime/React-RCTRuntime.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/RNReanimated/RNReanimated.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React/React-Core.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactAppDependencyProvider/ReactAppDependencyProvider.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCodegen/ReactCodegen.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_FabricComponents/React-FabricComponents.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/SDWebImage/SDWebImage.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/SDWebImageAVIFCoder/SDWebImageAVIFCoder.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/glog/glog.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_tracing/React-jsinspectortracing.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/libavif/libavif.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_defaults/React-defaultsnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_dom/React-domnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_featureflags/React-featureflagsnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_idlecallbacks/React-idlecallbacksnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_microtasks/React-microtasksnativemodule.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_css/React-renderercss.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_runtime/React-jsitooling.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/reacthermes/React-hermes.modulemap" -Xcc -fmodule-map-file="${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
SWIFT_INCLUDE_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/EXConstants" "${PODS_CONFIGURATION_BUILD_DIR}/Expo" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoAsset" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoBlur" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoCrypto" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoFont" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoHaptics" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoHead" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoImage" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoKeepAwake" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinearGradient" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoLinking" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSecureStore" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSplashScreen" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSymbols" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoSystemUI" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoVideo" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoWebBrowser"
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
