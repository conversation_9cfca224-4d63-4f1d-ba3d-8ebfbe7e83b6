/**
 * Automatically generated by expo-modules-autolinking.
 *
 * This autogenerated class provides a list of classes of native Expo modules,
 * but only these that are written in Swift and use the new API for creating Expo modules.
 */

import ExpoModulesCore
import Expo
import ExpoAsset
import ExpoBlur
import EXConstants
import ExpoFileSystem
import ExpoFont
import ExpoHaptics
import ExpoImage
import ExpoKeepAwake
import ExpoLinearGradient
import ExpoLinking
import ExpoHead
import ExpoSecureStore
import ExpoSplashScreen
import ExpoSymbols
import ExpoSystemUI
import ExpoVideo
import ExpoWebBrowser

@objc(ExpoModulesProvider)
public class ExpoModulesProvider: ModulesProvider {
  public override func getModuleClasses() -> [AnyModule.Type] {
    return [
      ExpoFetchModule.self,
      AssetModule.self,
      BlurViewModule.self,
      ConstantsModule.self,
      FileSystemModule.self,
      FileSystemNextModule.self,
      FontLoaderModule.self,
      FontUtilsModule.self,
      HapticsModule.self,
      ImageModule.self,
      KeepAwakeModule.self,
      LinearGradientModule.self,
      ExpoLinkingModule.self,
      ExpoHeadModule.self,
      SecureStoreModule.self,
      SplashScreenModule.self,
      SymbolModule.self,
      ExpoSystemUIModule.self,
      VideoModule.self,
      WebBrowserModule.self
    ]
  }

  public override func getAppDelegateSubscribers() -> [ExpoAppDelegateSubscriber.Type] {
    return [
      FileSystemBackgroundSessionHandler.self,
      LinkingAppDelegateSubscriber.self,
      ExpoHeadAppDelegateSubscriber.self,
      SplashScreenAppDelegateSubscriber.self
    ]
  }

  public override func getReactDelegateHandlers() -> [ExpoReactDelegateHandlerTupleType] {
    return [
    ]
  }

  public override func getAppCodeSignEntitlements() -> AppCodeSignEntitlements {
    return AppCodeSignEntitlements.from(json: #"{}"#)
  }
}
